import React, { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Divider,
    <PERSON>,
    Typography,
    DialogTitle,
} from "@vapor/v3-components";
import { Close } from "@mui/icons-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import Spinner from "../../../../custom-components/Spinner";
import ToMergeListFilters from "../../../archive/components/ToMergeListFilters";
import { useToMergeListData } from "../../../archive/hooks/useToMergeListData";
import BottomToastNotification from "../../../../custom-components/Notification";
import usePostCustom from "../../../../hooks/usePostCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import ConfirmModal from "../../../../custom-components/ConfirmModal";

interface TrasferimentoFilePraticaDrawerProps {
    open: boolean;
    onClose: () => void;
    fileData: {
        id: string;
        nomefile: string;
        titolodocumento?: string;
        datadoc?: string;
    } | null;
}

const TrasferimentoFilePraticaDrawer: React.FC<TrasferimentoFilePraticaDrawerProps> = ({
    open,
    onClose,
    fileData,
}) => {
    const { t } = useTranslation();

    // State for confirmation modal
    const [confirmModalOpen, setConfirmModalOpen] = useState(false);
    const [selectedPractice, setSelectedPractice] = useState<any>(null);
    const [transferError, setTransferError] = useState<string | null>(null);
    const [showSuccessToast, setShowSuccessToast] = useState(false);

    // API hook for file transfer
    const copyFileRequest = usePostCustom("anagrafiche/copyfile");

    // Use the to-merge-list data hook
    const {
        query,
        setQuery,
        list,
        loading,
        resetFilters,
    } = useToMergeListData();



    useEffect(() => {
        if (open) {
            resetFilters();
        }
    }, [open, resetFilters]);


    const handleRowClick = (uniqueid: string) => {
        const practice = list.rows?.find((row: any) => row?.uniqueid === uniqueid);
        if (practice) {
            setSelectedPractice(practice);
            setTransferError(null);
            setShowSuccessToast(false);
            setConfirmModalOpen(true);
        }
    };

    const handleConfirmTransfer = async () => {
        if (!selectedPractice?.uniqueid || !fileData?.id) return;
        try {
            setTransferError(null);
            await copyFileRequest.doFetch(true, {
                id: selectedPractice.id,
                fileId: fileData.id
            });

            // Close the modal first
            setConfirmModalOpen(false);
            setSelectedPractice(null);
            
            // Then show the success toast
            setShowSuccessToast(true);
            
            // Close the drawer after a delay
            setTimeout(() => {
                onClose();
            }, 1500);
        } catch (error) {
            setTransferError(t("Errore durante il trasferimento del file. Riprova."));
        }
    };

    const handleCancelTransfer = () => {
        setConfirmModalOpen(false);
        setSelectedPractice(null);
        setTransferError(null);
        setShowSuccessToast(false);
    };



    return (
        <Drawer
            anchor="right"
            open={open}
            onClose={onClose}
            hideBackdrop
            sx={{
                "& .MuiDrawer-paper": {
                    width: "40%",
                },
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    px: 3,
                    py: 2,
                }}
            >
                <DialogTitle>
                    {t("Trasferimento file nella pratica")}
                </DialogTitle>
                <IconButton
                    onClick={onClose}
                    color="primary"
                >
                    <Close />
                </IconButton>
            </Box>
            <Divider />

            <Box
                sx={{
                    backgroundColor: "white",
                    flexGrow: 1,
                    p: 2,
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                }}
            >
                <BottomToastNotification
                    showNotification={true}
                    text={t("Il file visualizzato sarà copiato nella cartella principale dei documenti della pratica. Avviare una ricerca o mostrare tutte le pratiche per visualizzare la lista.")}
                    notificationVariant="inline"
                    severity="info"
                    variant="outlined"
                />

                <ToMergeListFilters
                    query={query}
                    setQuery={setQuery}
                    onReset={resetFilters}
                />

                <Box
                    sx={{
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                        overflow: "hidden",
                    }}
                >
                    {loading ? (
                        <Spinner fullPage={false} />
                    ) : (
                        <>
                            {list.rows && list.rows.length > 0 ? (
                                <CustomDataGrid
                                    columns={list.columns || []}
                                    data={list.rows.filter(row => row !== null && typeof row === 'object')}
                                    totalRows={list.totalRows || 0}
                                    page={list.page || 0}
                                    pageSize={list.pageSize || 10}
                                    loading={loading}
                                    query={query}
                                    onClickCallback={handleRowClick}
                                    onClickKey="uniqueid"
                                    hasAdditionaStyles={false}
                                    hideToolbar={true}
                                    hideFooter={true}
                                    disablePagination={true}
                                    disableSorting={true}
                                    disableColumnReorder={true}
                                    disableColumnResize={true}
                                    disableColumnMenu={true}
                                    hidePaginationText={true}
                                />
                            ) : (
                                <Box sx={{ minHeight: 200 }} />
                            )}
                        </>
                    )}
                </Box>
            </Box>

            <ConfirmModal
                open={confirmModalOpen}
                title={t("Conferma")}
                handleDecline={handleCancelTransfer}
                handleAgree={handleConfirmTransfer}
                decline={t("Annulla")}
                agree={copyFileRequest.loading ? t("Conferma") : t("Conferma")}
                loading={copyFileRequest.loading}
                maxWidth="sm"
                fullWidth={true}
            >
                {transferError && (
                    <Box sx={{ mb: 2 }}>
                        <BottomToastNotification
                            showNotification={true}
                            text={transferError}
                            notificationVariant="inline"
                            severity="error"
                            variant="outlined"
                        />
                    </Box>
                )}
                <Typography variant="body1" sx={{ mb: 2 }}>
                    {t("Sei sicuro di voler trasferire il file")} <strong>{fileData?.nomefile || ""}</strong> {t("nella pratica")} <strong>{selectedPractice?.codicearchivio || ""}</strong>?
                </Typography>
            </ConfirmModal>

            <ToastNotification
                showNotification={showSuccessToast}
                setShowNotification={setShowSuccessToast}
                text={t("Trasferimento completato")}
                severity="success"
            />
        </Drawer>
    );
};

export default TrasferimentoFilePraticaDrawer;